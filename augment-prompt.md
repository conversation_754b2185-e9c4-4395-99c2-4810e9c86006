The `PhotosTabContent` in "lib/search/widgets/photos_tab_content.dart" has issue.

Expected behavior:
When the scrollbar of `PhotosTabContent` TabView is scrolled-down, the `SliverAppBar` inside `NestedScrollView` in "lib/search/widgets/search_screen_active.dart" that contains `<PERSON><PERSON>ield` should be hidden.

Current behavior:
- When `PhotosTabContent` TabView is still in loading state, and then I scroll-down the scrollbar, the `SliverAppBar` in `NestedScrollView` inside `SearchScreenActive` widget is hidden correctly.
- But when `PhotosTabContent` TabView data has been loaded, and then I scroll down its scrollbar, the `SliverAppBar` in `NestedScrollView` inside `SearchScreenActive` wdiget stays pinned/visible.

The cause:
- When `PhotosTabContent` tab view in loading state, the scroll is still owned by `NestedScrollView` in `SearchScreenActive` widget. So the sliver behavior works properly.
- But when `PhotosTabContent` tab view data is loaded, what I scroll becomes the `MasonryGridView` of that `PhotosTabContent` tab.

The solution:
Similar solution has been implemented to `CategoriesTabContent` in "lib/search/widgets/categories_tab_content.dart".
You can check for its pattern and implement the solution to `PhotosTabContent`.

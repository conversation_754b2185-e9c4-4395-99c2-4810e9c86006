import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/camera/dto/camera_data.dart';
import 'package:portraitmode/camera/http_responses/camera_list_response.dart';
import 'package:portraitmode/camera/services/camera_list_service.dart';
import 'package:portraitmode/camera/widgets/camera_list_item.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/search/dto/search_data.dart';
import 'package:portraitmode/search/providers/search_cameras_data_provider.dart';
import 'package:portraitmode/search/providers/search_cameras_provider.dart';

class CamerasTabContent extends ConsumerStatefulWidget {
  const CamerasTabContent({
    super.key,
    required this.searchData,
    required this.keyword,
    required this.dataList,
  });

  final CamerasSearchData searchData;
  final String keyword;
  final List<CameraData> dataList;

  @override
  CamerasTabContentState createState() => CamerasTabContentState();
}

class CamerasTabContentState extends ConsumerState<CamerasTabContent> {
  final _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();
  final _scrollController = ScrollController();
  final _cameraListService = CameraListService();

  /// Number of load more per page.
  ///
  /// We will exclude cameras with photos less than 3.
  /// Currently, there's no straight-forward / "out of the box" way to do this.
  ///
  /// That's why, in the camera query, we will exclude cameras with photos less than 3
  /// which means, the photos returned could be less than `limit`.
  ///
  /// To prevent a very low number of photos returned, we will set the `limit` to 50.
  final int _loadMorePerPage = 50;
  bool _isLoadingMore = false;

  double? _loadMoreThreshold;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_canLoadMore()) _triggerLoadMore();
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_canLoadMore()) _triggerLoadMore();
  }

  double _getLoadMoreThreshold() {
    if (_loadMoreThreshold == null) {
      final screenHeight = MediaQuery.of(context).size.height;
      final dynamicThreshold = screenHeight * LoadMoreConfig.tresholdByScreen;

      // Add bounds by min and max.
      _loadMoreThreshold = math.max(
        math.min(dynamicThreshold, LoadMoreConfig.maxTreshold),
        LoadMoreConfig.minTreshold,
      );
    }
    return _loadMoreThreshold!;
  }

  bool _canLoadMore() {
    return _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent -
                _getLoadMoreThreshold() &&
        !_isLoadingMore &&
        !widget.searchData.loadMoreEndReached;
  }

  void _triggerLoadMore() async {
    if (_isLoadingMore || widget.searchData.loadMoreEndReached) return;

    if (mounted) {
      setState(() {
        _isLoadingMore = true;
      });
    }

    await _handleLoadMore();

    if (mounted) {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  double? _cacheExtent;

  @override
  Widget build(BuildContext context) {
    // log('build screen: CamerasTabContent');

    if (_cacheExtent == null) {
      final double screenHeight = MediaQuery.sizeOf(context).height;
      _cacheExtent = screenHeight * 2.5;
    }

    return Container(
      constraints: const BoxConstraints(maxWidth: 768.0),
      child: RefreshIndicator(
        key: _refreshIndicatorKey,
        onRefresh: _handleRefresh,
        child: GridView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.all(8.0),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            mainAxisSpacing: 8.0,
            crossAxisSpacing: 8.0,
            childAspectRatio: 1.8,
          ),
          cacheExtent: _cacheExtent,
          itemCount:
              widget.dataList.length +
              (_isLoadingMore && !widget.searchData.loadMoreEndReached ? 1 : 0),
          itemBuilder: (BuildContext context, int index) {
            // Handle loading indicator as the last item
            if (index == widget.dataList.length) {
              return Container(
                padding: const EdgeInsets.all(16.0),
                alignment: Alignment.center,
                child: CircularProgressIndicator(
                  color: context.colors.baseColorAlt,
                ),
              );
            }

            return CameraListItem(
              key: ValueKey(widget.dataList[index].id),
              camera: widget.dataList[index],
            );
          },
        ),
      ),
    );
  }

  Future<void> _handleRefresh() async {
    // Reset loading state
    _isLoadingMore = false;
    late CameraListResponse response;

    // log('The cameras search keyword onRefresh is: ${widget.keyword}');

    if (widget.keyword.isNotEmpty) {
      response = await _cameraListService.fetch(
        limit: _loadMorePerPage,
        lastId: null,
        keyword: widget.keyword,
      );
    } else {
      response = await _cameraListService.fetch(
        limit: _loadMorePerPage,
        lastId: null,
      );
    }

    _handleCameraListResponse(response, true, false);
  }

  Future<void> _handleLoadMore() async {
    late CameraListResponse response;

    // log('The cameras search keyword is: "${widget.keyword}"');

    if (widget.keyword.isNotEmpty) {
      response = await _cameraListService.fetch(
        limit: _loadMorePerPage,
        lastId: widget.searchData.lastId,
        keyword: widget.keyword,
      );
    } else {
      response = await _cameraListService.fetch(
        limit: _loadMorePerPage,
        lastId: widget.searchData.lastId,
      );
    }

    final isFirstLoad = widget.searchData.lastId == null;

    _handleCameraListResponse(response, false, isFirstLoad);
  }

  void _handleCameraListResponse(
    CameraListResponse response,
    bool isRefresh,
    bool isFirstLoad,
  ) {
    // In case user navigates back immediately before async request complete.
    if (!mounted) return;

    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    final searchCamerasReactiveService = ref.read(
      searchCamerasReactiveServiceProvider,
    );

    if (response.data.isEmpty) {
      if (isRefresh) {
        searchCamerasReactiveService.clear();
      }

      ref.read(searchCamerasDataProvider.notifier).setLoadMoreEndReached(true);
      return;
    }

    ref
        .read(searchCamerasDataProvider.notifier)
        .setLastId(response.data.last.id);

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh) {
      searchCamerasReactiveService.replaceAll(response.data);
    } else {
      if (isFirstLoad) {
        searchCamerasReactiveService.replaceAll(response.data);
      } else {
        searchCamerasReactiveService.addItems(response.data);
      }
    }

    if (response.data.length < _loadMorePerPage) {
      ref.read(searchCamerasDataProvider.notifier).setLoadMoreEndReached(true);
    } else {
      ref.read(searchCamerasDataProvider.notifier).setLoadMoreEndReached(false);
    }
  }
}
